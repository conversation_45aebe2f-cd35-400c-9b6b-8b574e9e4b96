{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs)\n}\n\n// Button variants for consistent styling across the app\nexport const buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        neon: \"bg-gradient-to-r from-neon-cyan to-neon-purple text-white hover:shadow-[0_0_20px_rgba(6,182,212,0.5)] transition-all duration-300\",\n        chaos: \"bg-gradient-to-r from-neon-pink via-chaos-orange to-electric-blue text-white hover:shadow-[0_0_30px_rgba(236,72,153,0.6)] transition-all duration-300 animate-pulse\",\n        game: \"bg-gradient-to-r from-primary to-accent text-white hover:shadow-[0_0_25px_rgba(99,102,241,0.5)] transition-all duration-300\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\n// Card variants for different content types\nexport const cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        elevated: \"shadow-lg hover:shadow-xl\",\n        neon: \"border-neon-cyan/30 bg-gradient-to-br from-background to-muted hover:border-neon-cyan/50 hover:shadow-[0_0_20px_rgba(6,182,212,0.2)]\",\n        game: \"border-primary/30 bg-gradient-to-br from-background to-primary/5 hover:border-primary/50 hover:shadow-[0_0_20px_rgba(99,102,241,0.2)]\",\n        project: \"border-accent/30 bg-gradient-to-br from-background to-accent/5 hover:border-accent/50 hover:shadow-[0_0_20px_rgba(16,185,129,0.2)] hover:scale-[1.02]\",\n      },\n      padding: {\n        none: \"p-0\",\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\n// Text variants for consistent typography\nexport const textVariants = cva(\"\", {\n  variants: {\n    variant: {\n      default: \"text-foreground\",\n      muted: \"text-muted-foreground\",\n      accent: \"text-accent\",\n      primary: \"text-primary\",\n      secondary: \"text-secondary\",\n      destructive: \"text-destructive\",\n      neon: \"text-transparent bg-clip-text bg-gradient-to-r from-neon-cyan to-neon-purple\",\n      chaos: \"text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-chaos-orange to-electric-blue\",\n      game: \"text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent\",\n    },\n    size: {\n      xs: \"text-xs\",\n      sm: \"text-sm\",\n      base: \"text-base\",\n      lg: \"text-lg\",\n      xl: \"text-xl\",\n      \"2xl\": \"text-2xl\",\n      \"3xl\": \"text-3xl\",\n      \"4xl\": \"text-4xl\",\n      \"5xl\": \"text-5xl\",\n      \"6xl\": \"text-6xl\",\n    },\n    weight: {\n      light: \"font-light\",\n      normal: \"font-normal\",\n      medium: \"font-medium\",\n      semibold: \"font-semibold\",\n      bold: \"font-bold\",\n      extrabold: \"font-extrabold\",\n    },\n    family: {\n      sans: \"font-sans\",\n      mono: \"font-mono\",\n      display: \"font-display\",\n    },\n  },\n  defaultVariants: {\n    variant: \"default\",\n    size: \"base\",\n    weight: \"normal\",\n    family: \"sans\",\n  },\n})\n\n// Animation utilities\nexport const animations = {\n  fadeIn: \"animate-in fade-in duration-500\",\n  slideUp: \"animate-in slide-in-from-bottom-4 duration-500\",\n  slideDown: \"animate-in slide-in-from-top-4 duration-500\",\n  slideLeft: \"animate-in slide-in-from-right-4 duration-500\",\n  slideRight: \"animate-in slide-in-from-left-4 duration-500\",\n  scaleIn: \"animate-in zoom-in-95 duration-300\",\n  bounce: \"animate-bounce\",\n  pulse: \"animate-pulse\",\n  spin: \"animate-spin\",\n  ping: \"animate-ping\",\n}\n\n// Utility functions\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date)\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat(\"en-US\", {\n    style: \"currency\",\n    currency: \"USD\",\n  }).format(amount)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, \"\")\n    .replace(/[\\s_-]+/g, \"-\")\n    .replace(/^-+|-+$/g, \"\")\n}\n\nexport function truncate(text: string, length: number): string {\n  if (text.length <= length) return text\n  return text.slice(0, length) + \"...\"\n}\n\n// Type exports for component props\nexport type ButtonVariants = VariantProps<typeof buttonVariants>\nexport type CardVariants = VariantProps<typeof cardVariants>\nexport type TextVariants = VariantProps<typeof textVariants>\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAGO,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAC9B,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAIK,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAC5B,wFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,MAAM;YACN,MAAM;YACN,SAAS;QACX;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AAIK,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAAE,IAAI;IAClC,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;YACT,WAAW;YACX,aAAa;YACb,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA,QAAQ;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,MAAM;YACN,WAAW;QACb;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;AACR;AAGO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn, buttonVariants, type ButtonVariants } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    ButtonVariants {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { Menu, X, Gamepad2, Zap } from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { cn } from \"@/lib/utils\"\n\nconst navigation = [\n  { name: \"Home\", href: \"/\" },\n  { name: \"About\", href: \"/about\" },\n  { name: \"Projects\", href: \"/projects\" },\n  { name: \"Blog\", href: \"/blog\" },\n  { name: \"Contact\", href: \"/contact\" },\n]\n\nexport function Header() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"relative\">\n              <Gamepad2 className=\"h-8 w-8 text-primary transition-colors group-hover:text-neon-cyan\" />\n              <Zap className=\"absolute -top-1 -right-1 h-4 w-4 text-secondary animate-pulse\" />\n            </div>\n            <span className=\"text-xl font-display font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n              Chaos Studios\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  \"text-sm font-medium transition-colors hover:text-primary relative group\",\n                  pathname === item.href\n                    ? \"text-primary\"\n                    : \"text-muted-foreground\"\n                )}\n              >\n                {item.name}\n                {pathname === item.href && (\n                  <motion.div\n                    layoutId=\"activeTab\"\n                    className=\"absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent\"\n                    initial={false}\n                    transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                  />\n                )}\n                <div className=\"absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent scale-x-0 group-hover:scale-x-100 transition-transform duration-300\" />\n              </Link>\n            ))}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button asChild variant=\"game\" size=\"sm\">\n              <Link href=\"/request-quote\">\n                Request Quote\n              </Link>\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden\"\n            onClick={() => setIsOpen(!isOpen)}\n            aria-label=\"Toggle menu\"\n          >\n            {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n          </Button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden border-t border-border/40 bg-background/95 backdrop-blur\"\n          >\n            <div className=\"container mx-auto px-4 py-4\">\n              <nav className=\"flex flex-col space-y-4\">\n                {navigation.map((item, index) => (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                  >\n                    <Link\n                      href={item.href}\n                      className={cn(\n                        \"block text-sm font-medium transition-colors hover:text-primary\",\n                        pathname === item.href\n                          ? \"text-primary\"\n                          : \"text-muted-foreground\"\n                      )}\n                      onClick={() => setIsOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  </motion.div>\n                ))}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: navigation.length * 0.1 }}\n                  className=\"pt-4 border-t border-border/40\"\n                >\n                  <Button asChild variant=\"game\" size=\"sm\" className=\"w-full\">\n                    <Link href=\"/request-quote\" onClick={() => setIsOpen(false)}>\n                      Request Quote\n                    </Link>\n                  </Button>\n                </motion.div>\n              </nav>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AARA;;;;;;;;;AAUA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAK,WAAU;8CAAuG;;;;;;;;;;;;sCAMzH,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2EACA,aAAa,KAAK,IAAI,GAClB,iBACA;;wCAGL,KAAK,IAAI;wCACT,aAAa,KAAK,IAAI,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,UAAS;4CACT,WAAU;4CACV,SAAS;4CACT,YAAY;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;;;;;;sDAG9D,8OAAC;4CAAI,WAAU;;;;;;;mCAlBV,KAAK,IAAI;;;;;;;;;;sCAwBpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kJAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAO,MAAK;0CAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAiB;;;;;;;;;;;;;;;;sCAOhC,8OAAC,kJAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,UAAU,CAAC;4BAC1B,cAAW;sCAEV,uBAAS,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;kDAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA,aAAa,KAAK,IAAI,GAClB,iBACA;4CAEN,SAAS,IAAM,UAAU;sDAExB,KAAK,IAAI;;;;;;uCAfP,KAAK,IAAI;;;;;8CAmBlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,WAAW,MAAM,GAAG;oCAAI;oCAC7C,WAAU;8CAEV,cAAA,8OAAC,kJAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAO,MAAK;wCAAK,WAAU;kDACjD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAiB,SAAS,IAAM,UAAU;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjF", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Gamepad2, Zap, Github, Twitter, Linkedin, Mail, MapPin, Phone } from \"lucide-react\"\n\nconst footerLinks = {\n  company: [\n    { name: \"About Us\", href: \"/about\" },\n    { name: \"Our Team\", href: \"/about#team\" },\n    { name: \"Careers\", href: \"/careers\" },\n    { name: \"Press Kit\", href: \"/press\" },\n  ],\n  services: [\n    { name: \"Game Development\", href: \"/services/game-development\" },\n    { name: \"Asset Creation\", href: \"/services/asset-creation\" },\n    { name: \"Consulting\", href: \"/services/consulting\" },\n    { name: \"Porting\", href: \"/services/porting\" },\n  ],\n  resources: [\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Case Studies\", href: \"/case-studies\" },\n    { name: \"Documentation\", href: \"/docs\" },\n    { name: \"Support\", href: \"/support\" },\n  ],\n  legal: [\n    { name: \"Privacy Policy\", href: \"/privacy\" },\n    { name: \"Terms of Service\", href: \"/terms\" },\n    { name: \"Cookie Policy\", href: \"/cookies\" },\n    { name: \"GDPR\", href: \"/gdpr\" },\n  ],\n}\n\nconst socialLinks = [\n  { name: \"GitHub\", href: \"https://github.com/chaosstudios\", icon: Github },\n  { name: \"Twitter\", href: \"https://twitter.com/chaosstudios\", icon: Twitter },\n  { name: \"LinkedIn\", href: \"https://linkedin.com/company/chaosstudios\", icon: Linkedin },\n  { name: \"Email\", href: \"mailto:<EMAIL>\", icon: Mail },\n]\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-muted/30 border-t border-border/40\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-12 lg:py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 group mb-4\">\n                <div className=\"relative\">\n                  <Gamepad2 className=\"h-8 w-8 text-primary transition-colors group-hover:text-neon-cyan\" />\n                  <Zap className=\"absolute -top-1 -right-1 h-4 w-4 text-secondary animate-pulse\" />\n                </div>\n                <span className=\"text-xl font-display font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n                  Chaos Studios\n                </span>\n              </Link>\n              <p className=\"text-muted-foreground mb-6 max-w-sm\">\n                Crafting chaotic-strategic games and interactive experiences that push the boundaries of digital entertainment.\n              </p>\n              \n              {/* Contact Info */}\n              <div className=\"space-y-2 text-sm text-muted-foreground\">\n                <div className=\"flex items-center space-x-2\">\n                  <MapPin className=\"h-4 w-4\" />\n                  <span>San Francisco, CA</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Phone className=\"h-4 w-4\" />\n                  <span>+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Mail className=\"h-4 w-4\" />\n                  <span><EMAIL></span>\n                </div>\n              </div>\n            </div>\n\n            {/* Links Sections */}\n            <div>\n              <h3 className=\"font-semibold text-foreground mb-4\">Company</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-foreground mb-4\">Services</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-foreground mb-4\">Resources</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-foreground mb-4\">Legal</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"py-6 border-t border-border/40\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-sm text-muted-foreground\">\n              © {new Date().getFullYear()} Chaos Studios. All rights reserved.\n            </div>\n            \n            {/* Social Links */}\n            <div className=\"flex items-center space-x-4\">\n              {socialLinks.map((social) => {\n                const Icon = social.icon\n                return (\n                  <Link\n                    key={social.name}\n                    href={social.href}\n                    className=\"text-muted-foreground hover:text-primary transition-colors\"\n                    aria-label={social.name}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAS;QACnC;YAAE,MAAM;YAAY,MAAM;QAAc;QACxC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAa,MAAM;QAAS;KACrC;IACD,UAAU;QACR;YAAE,MAAM;YAAoB,MAAM;QAA6B;QAC/D;YAAE,MAAM;YAAkB,MAAM;QAA2B;QAC3D;YAAE,MAAM;YAAc,MAAM;QAAuB;QACnD;YAAE,MAAM;YAAW,MAAM;QAAoB;KAC9C;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAgB,MAAM;QAAgB;QAC9C;YAAE,MAAM;YAAiB,MAAM;QAAQ;QACvC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,OAAO;QACL;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAoB,MAAM;QAAS;QAC3C;YAAE,MAAM;YAAiB,MAAM;QAAW;QAC1C;YAAE,MAAM;YAAQ,MAAM;QAAQ;KAC/B;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAU,MAAM;QAAmC,MAAM,sMAAA,CAAA,SAAM;IAAC;IACxE;QAAE,MAAM;QAAW,MAAM;QAAoC,MAAM,wMAAA,CAAA,UAAO;IAAC;IAC3E;QAAE,MAAM;QAAY,MAAM;QAA6C,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACtF;QAAE,MAAM;QAAS,MAAM;QAAiC,MAAM,kMAAA,CAAA,OAAI;IAAC;CACpE;AAEM,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;0DAEjB,8OAAC;gDAAK,WAAU;0DAAuG;;;;;;;;;;;;kDAIzH,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;kDAKnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAgC;oCAC1C,IAAI,OAAO,WAAW;oCAAG;;;;;;;0CAI9B,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC;oCAChB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,OAAO,IAAI;wCACjB,WAAU;wCACV,cAAY,OAAO,IAAI;kDAEvB,cAAA,8OAAC;4CAAK,WAAU;;;;;;uCALX,OAAO,IAAI;;;;;gCAQtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 1043, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/layout/main-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { <PERSON><PERSON> } from \"./header\"\nimport { Footer } from \"./footer\"\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nconst pageVariants = {\n  initial: {\n    opacity: 0,\n    y: 20,\n  },\n  in: {\n    opacity: 1,\n    y: 0,\n  },\n  out: {\n    opacity: 0,\n    y: -20,\n  },\n}\n\nconst pageTransition = {\n  type: \"tween\",\n  ease: \"anticipate\",\n  duration: 0.5,\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header />\n      <motion.main\n        className=\"flex-1\"\n        initial=\"initial\"\n        animate=\"in\"\n        exit=\"out\"\n        variants={pageVariants}\n        transition={pageTransition}\n      >\n        {children}\n      </motion.main>\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUA,MAAM,eAAe;IACnB,SAAS;QACP,SAAS;QACT,GAAG;IACL;IACA,IAAI;QACF,SAAS;QACT,GAAG;IACL;IACA,KAAK;QACH,SAAS;QACT,GAAG,CAAC;IACN;AACF;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BACP,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,WAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,MAAK;gBACL,UAAU;gBACV,YAAY;0BAEX;;;;;;0BAEH,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn, cardVariants, type CardVariants } from \"@/lib/utils\"\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    CardVariants {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, padding, className }))}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE;YAAE;YAAS;YAAS;QAAU;QACxD,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        neon: \"border-neon-cyan/30 bg-neon-cyan/10 text-neon-cyan hover:bg-neon-cyan/20\",\n        game: \"border-primary/30 bg-primary/10 text-primary hover:bg-primary/20\",\n        accent: \"border-accent/30 bg-accent/10 text-accent hover:bg-accent/20\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,MAAM;YACN,MAAM;YACN,QAAQ;QACV;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/lib/data.ts"], "sourcesContent": ["// Sample data for the website\n\nexport interface Project {\n  id: string\n  title: string\n  description: string\n  image: string\n  category: string\n  status: \"completed\" | \"in-progress\" | \"upcoming\"\n  technologies: string[]\n  featured: boolean\n  link?: string\n}\n\nexport interface TeamMember {\n  id: string\n  name: string\n  role: string\n  bio: string\n  image: string\n  social: {\n    twitter?: string\n    linkedin?: string\n    github?: string\n  }\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  excerpt: string\n  content: string\n  author: string\n  publishedAt: string\n  category: string\n  tags: string[]\n  featured: boolean\n  image: string\n}\n\n// Sample Projects Data\nexport const projects: Project[] = [\n  {\n    id: \"pigeon-turf-wars\",\n    title: \"Pigeon Turf Wars\",\n    description: \"A chaotic-strategic multiplayer game where players control flocks of pigeons competing for urban territory. Features real-time strategy elements with unpredictable AI behaviors.\",\n    image: \"/projects/pigeon-turf-wars.jpg\",\n    category: \"Strategy\",\n    status: \"completed\",\n    technologies: [\"Unity\", \"C#\", \"Photon\", \"Steam API\"],\n    featured: true,\n    link: \"https://store.steampowered.com/app/pigeon-turf-wars\"\n  },\n  {\n    id: \"neon-runner\",\n    title: \"Neon Runner\",\n    description: \"A fast-paced cyberpunk endless runner with dynamic music synchronization and procedurally generated obstacles. Built for mobile and PC platforms.\",\n    image: \"/projects/neon-runner.jpg\",\n    category: \"Action\",\n    status: \"completed\",\n    technologies: [\"Unreal Engine\", \"Blueprint\", \"Mobile SDK\"],\n    featured: true\n  },\n  {\n    id: \"quantum-chess\",\n    title: \"Quantum Chess\",\n    description: \"Traditional chess meets quantum mechanics. Pieces exist in superposition states until observed, creating mind-bending strategic possibilities.\",\n    image: \"/projects/quantum-chess.jpg\",\n    category: \"Puzzle\",\n    status: \"in-progress\",\n    technologies: [\"React\", \"Three.js\", \"WebGL\", \"Node.js\"],\n    featured: true\n  },\n  {\n    id: \"mystic-forge\",\n    title: \"Mystic Forge\",\n    description: \"A magical crafting simulation where players discover recipes through experimentation and intuition rather than following guides.\",\n    image: \"/projects/mystic-forge.jpg\",\n    category: \"Simulation\",\n    status: \"upcoming\",\n    technologies: [\"Godot\", \"GDScript\", \"SQLite\"],\n    featured: false\n  }\n]\n\n// Sample Team Members Data\nexport const teamMembers: TeamMember[] = [\n  {\n    id: \"alex-chen\",\n    name: \"Alex Chen\",\n    role: \"Creative Director & Founder\",\n    bio: \"Former AAA game designer with 10+ years experience. Led development teams at major studios before founding Chaos Studios to create innovative indie experiences.\",\n    image: \"/team/alex-chen.jpg\",\n    social: {\n      twitter: \"@alexchen_dev\",\n      linkedin: \"alexchen-gamedev\",\n      github: \"alexchen\"\n    }\n  },\n  {\n    id: \"maya-rodriguez\",\n    name: \"Maya Rodriguez\",\n    role: \"Lead Developer\",\n    bio: \"Full-stack developer specializing in game engines and real-time systems. Expert in Unity, Unreal Engine, and custom engine development.\",\n    image: \"/team/maya-rodriguez.jpg\",\n    social: {\n      twitter: \"@maya_codes\",\n      linkedin: \"maya-rodriguez-dev\",\n      github: \"mayarod\"\n    }\n  },\n  {\n    id: \"jordan-kim\",\n    name: \"Jordan Kim\",\n    role: \"Art Director\",\n    bio: \"Visual artist with a passion for creating immersive game worlds. Specializes in concept art, 3D modeling, and procedural generation techniques.\",\n    image: \"/team/jordan-kim.jpg\",\n    social: {\n      linkedin: \"jordan-kim-art\",\n      twitter: \"@jordankim_art\"\n    }\n  },\n  {\n    id: \"sam-okafor\",\n    name: \"Sam Okafor\",\n    role: \"Audio Designer\",\n    bio: \"Sound engineer and composer creating dynamic audio experiences. Expert in adaptive music systems and spatial audio for immersive gameplay.\",\n    image: \"/team/sam-okafor.jpg\",\n    social: {\n      twitter: \"@sam_audio\",\n      linkedin: \"sam-okafor-audio\"\n    }\n  }\n]\n\n// Sample Blog Posts Data\nexport const blogPosts: BlogPost[] = [\n  {\n    id: \"chaos-in-game-design\",\n    title: \"Embracing Chaos in Game Design\",\n    excerpt: \"How unpredictability and emergent gameplay can create more engaging player experiences.\",\n    content: \"Full blog post content here...\",\n    author: \"Alex Chen\",\n    publishedAt: \"2024-01-15\",\n    category: \"Design\",\n    tags: [\"game design\", \"chaos theory\", \"emergent gameplay\"],\n    featured: true,\n    image: \"/blog/chaos-in-game-design.jpg\"\n  },\n  {\n    id: \"unity-optimization-tips\",\n    title: \"Unity Performance Optimization: 10 Essential Tips\",\n    excerpt: \"Practical techniques to improve your Unity game's performance across all platforms.\",\n    content: \"Full blog post content here...\",\n    author: \"Maya Rodriguez\",\n    publishedAt: \"2024-01-10\",\n    category: \"Development\",\n    tags: [\"unity\", \"optimization\", \"performance\"],\n    featured: true,\n    image: \"/blog/unity-optimization.jpg\"\n  },\n  {\n    id: \"procedural-art-generation\",\n    title: \"The Art of Procedural Generation\",\n    excerpt: \"Exploring how algorithms can create beautiful and diverse game art assets.\",\n    content: \"Full blog post content here...\",\n    author: \"Jordan Kim\",\n    publishedAt: \"2024-01-05\",\n    category: \"Art\",\n    tags: [\"procedural generation\", \"art\", \"algorithms\"],\n    featured: false,\n    image: \"/blog/procedural-art.jpg\"\n  }\n]\n\n// Helper functions\nexport function getFeaturedProjects(): Project[] {\n  return projects.filter(project => project.featured)\n}\n\nexport function getFeaturedBlogPosts(): BlogPost[] {\n  return blogPosts.filter(post => post.featured)\n}\n\nexport function getProjectsByCategory(category: string): Project[] {\n  return projects.filter(project => project.category === category)\n}\n\nexport function getProjectsByStatus(status: Project[\"status\"]): Project[] {\n  return projects.filter(project => project.status === status)\n}\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;;;;;;;AAyCvB,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,QAAQ;QACR,cAAc;YAAC;YAAS;YAAM;YAAU;SAAY;QACpD,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,QAAQ;QACR,cAAc;YAAC;YAAiB;YAAa;SAAa;QAC1D,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,QAAQ;QACR,cAAc;YAAC;YAAS;YAAY;YAAS;SAAU;QACvD,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,QAAQ;QACR,cAAc;YAAC;YAAS;YAAY;SAAS;QAC7C,UAAU;IACZ;CACD;AAGM,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,OAAO;QACP,QAAQ;YACN,SAAS;YACT,UAAU;YACV,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,OAAO;QACP,QAAQ;YACN,SAAS;YACT,UAAU;YACV,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,OAAO;QACP,QAAQ;YACN,UAAU;YACV,SAAS;QACX;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,KAAK;QACL,OAAO;QACP,QAAQ;YACN,SAAS;YACT,UAAU;QACZ;IACF;CACD;AAGM,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAe;YAAgB;SAAoB;QAC1D,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAS;YAAgB;SAAc;QAC9C,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,UAAU;QACV,MAAM;YAAC;YAAyB;YAAO;SAAa;QACpD,UAAU;QACV,OAAO;IACT;CACD;AAGM,SAAS;IACd,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;AACpD;AAEO,SAAS;IACd,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AAC/C;AAEO,SAAS,sBAAsB,QAAgB;IACpD,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACzD;AAEO,SAAS,oBAAoB,MAAyB;IAC3D,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;AACvD", "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/sections/featured-projects.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport Link from \"next/link\"\nimport Image from \"next/image\"\nimport { Button } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { ExternalLink, ArrowRight, Play, Code, Gamepad2 } from \"lucide-react\"\nimport { getFeaturedProjects } from \"@/lib/data\"\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.2,\n      delayChildren: 0.1,\n    },\n  },\n}\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 20 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.5,\n      ease: \"easeOut\",\n    },\n  },\n}\n\nconst statusIcons = {\n  completed: Play,\n  \"in-progress\": Code,\n  upcoming: Gamepad2,\n}\n\nconst statusColors = {\n  completed: \"bg-victory-green/10 text-victory-green border-victory-green/20\",\n  \"in-progress\": \"bg-electric-blue/10 text-electric-blue border-electric-blue/20\",\n  upcoming: \"bg-chaos-orange/10 text-chaos-orange border-chaos-orange/20\",\n}\n\nexport function FeaturedProjects() {\n  const featuredProjects = getFeaturedProjects()\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-background via-muted/20 to-background\">\n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-3xl md:text-4xl font-display font-bold mb-4\"\n          >\n            Featured <span className=\"text-neon-cyan\">Projects</span>\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-lg text-muted-foreground max-w-2xl mx-auto\"\n          >\n            Discover our latest games and interactive experiences that showcase our passion for innovative design.\n          </motion.p>\n        </motion.div>\n\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-50px\" }}\n          variants={containerVariants}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {featuredProjects.map((project, index) => {\n            const StatusIcon = statusIcons[project.status]\n            return (\n              <motion.div key={project.id} variants={itemVariants}>\n                <Card variant=\"project\" className=\"group h-full overflow-hidden\">\n                  <div className=\"relative aspect-video overflow-hidden\">\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 z-10\" />\n                    <div className=\"absolute inset-0 bg-muted flex items-center justify-center\">\n                      <Gamepad2 className=\"h-16 w-16 text-muted-foreground/30\" />\n                    </div>\n                    {/* Placeholder for project image */}\n                    <div className=\"absolute top-4 right-4 z-20\">\n                      <Badge className={statusColors[project.status]}>\n                        <StatusIcon className=\"h-3 w-3 mr-1\" />\n                        {project.status.replace(\"-\", \" \")}\n                      </Badge>\n                    </div>\n                  </div>\n                  \n                  <CardHeader>\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <CardTitle className=\"group-hover:text-primary transition-colors\">\n                          {project.title}\n                        </CardTitle>\n                        <Badge variant=\"outline\" className=\"mt-2\">\n                          {project.category}\n                        </Badge>\n                      </div>\n                    </div>\n                    <CardDescription className=\"line-clamp-3\">\n                      {project.description}\n                    </CardDescription>\n                  </CardHeader>\n\n                  <CardContent className=\"pt-0\">\n                    <div className=\"flex flex-wrap gap-2 mb-4\">\n                      {project.technologies.slice(0, 3).map((tech) => (\n                        <Badge key={tech} variant=\"secondary\" className=\"text-xs\">\n                          {tech}\n                        </Badge>\n                      ))}\n                      {project.technologies.length > 3 && (\n                        <Badge variant=\"secondary\" className=\"text-xs\">\n                          +{project.technologies.length - 3}\n                        </Badge>\n                      )}\n                    </div>\n\n                    <div className=\"flex gap-2\">\n                      <Button asChild variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                        <Link href={`/projects/${project.id}`}>\n                          Learn More\n                          <ArrowRight className=\"ml-2 h-4 w-4\" />\n                        </Link>\n                      </Button>\n                      {project.link && (\n                        <Button asChild variant=\"ghost\" size=\"sm\">\n                          <Link href={project.link} target=\"_blank\" rel=\"noopener noreferrer\">\n                            <ExternalLink className=\"h-4 w-4\" />\n                          </Link>\n                        </Button>\n                      )}\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            )\n          })}\n        </motion.div>\n\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          variants={itemVariants}\n          className=\"text-center mt-12\"\n        >\n          <Button asChild variant=\"neon\" size=\"lg\">\n            <Link href=\"/projects\">\n              View All Projects\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;AAWA,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;YACjB,eAAe;QACjB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;QACR;IACF;AACF;AAEA,MAAM,cAAc;IAClB,WAAW,kMAAA,CAAA,OAAI;IACf,eAAe,kMAAA,CAAA,OAAI;IACnB,UAAU,8MAAA,CAAA,WAAQ;AACpB;AAEA,MAAM,eAAe;IACnB,WAAW;IACX,eAAe;IACf,UAAU;AACZ;AAEO,SAAS;IACd,MAAM,mBAAmB,CAAA,GAAA,kHAAA,CAAA,sBAAmB,AAAD;IAE3C,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;oBACzC,UAAU;oBACV,WAAU;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;;gCACX;8CACU,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;sCAE5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAQ;oBACxC,UAAU;oBACV,WAAU;8BAET,iBAAiB,GAAG,CAAC,CAAC,SAAS;wBAC9B,MAAM,aAAa,WAAW,CAAC,QAAQ,MAAM,CAAC;wBAC9C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAkB,UAAU;sCACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAU,WAAU;;kDAChC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAGtB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAW,YAAY,CAAC,QAAQ,MAAM,CAAC;;sEAC5C,8OAAC;4DAAW,WAAU;;;;;;wDACrB,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;kDAKnC,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAClB,QAAQ,KAAK;;;;;;sEAEhB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;0DAIvB,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;kDAIxB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC,iIAAA,CAAA,QAAK;4DAAY,SAAQ;4DAAY,WAAU;sEAC7C;2DADS;;;;;oDAIb,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;;4DAAU;4DAC3C,QAAQ,YAAY,CAAC,MAAM,GAAG;;;;;;;;;;;;;0DAKtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kJAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,WAAU;kEACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;;gEAAE;8EAErC,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;oDAGzB,QAAQ,IAAI,kBACX,8OAAC,kJAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,SAAQ;wDAAQ,MAAK;kEACnC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,QAAQ,IAAI;4DAAE,QAAO;4DAAS,KAAI;sEAC5C,cAAA,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAxDrB,QAAQ,EAAE;;;;;oBAiE/B;;;;;;8BAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,UAAU;oBACV,WAAU;8BAEV,cAAA,8OAAC,kJAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,SAAQ;wBAAO,MAAK;kCAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;gCAAY;8CAErB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}]}