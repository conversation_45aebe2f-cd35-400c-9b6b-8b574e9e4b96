import type { Metada<PERSON> } from "next";
import { Inter, JetBrains_Mono, Orbitron } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-sans",
  subsets: ["latin"],
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-mono",
  subsets: ["latin"],
  display: "swap",
});

const orbitron = Orbitron({
  variable: "--font-display",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: "Chaos Studios - Game Development & Interactive Experiences",
    template: "%s | Chaos Studios",
  },
  description: "Professional game development studio specializing in chaotic-strategic games, interactive experiences, and digital innovation. From concept to launch, we bring your gaming vision to life.",
  keywords: [
    "game development",
    "indie games",
    "game studio",
    "interactive experiences",
    "game design",
    "unity development",
    "unreal engine",
    "mobile games",
    "pc games",
    "game consulting",
  ],
  authors: [{ name: "Chaos Studios" }],
  creator: "Chaos Studios",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://chaosstudios.dev",
    title: "Chaos Studios - Game Development & Interactive Experiences",
    description: "Professional game development studio specializing in chaotic-strategic games and interactive experiences.",
    siteName: "Chaos Studios",
  },
  twitter: {
    card: "summary_large_image",
    title: "Chaos Studios - Game Development Studio",
    description: "Professional game development studio specializing in chaotic-strategic games and interactive experiences.",
    creator: "@chaosstudios",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} ${orbitron.variable} antialiased min-h-screen bg-background font-sans`}
      >
        {children}
      </body>
    </html>
  );
}
