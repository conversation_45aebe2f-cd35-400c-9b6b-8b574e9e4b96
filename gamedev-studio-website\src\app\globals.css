@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&family=Orbitron:wght@400;500;600;700;800;900&display=swap');
@import "tailwindcss";

:root {
  /* Base Colors */
  --background: #0a0a0f;
  --foreground: #f8fafc;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --border: #334155;
  --input: #1e293b;
  --ring: #3b82f6;

  /* Brand Colors - Game Studio Theme */
  --primary: #6366f1; /* Indigo - Professional yet playful */
  --primary-foreground: #ffffff;
  --secondary: #f59e0b; /* Amber - Energy and creativity */
  --secondary-foreground: #0a0a0f;
  --accent: #10b981; /* Emerald - Success and growth */
  --accent-foreground: #ffffff;
  --destructive: #ef4444; /* Red - Alerts and warnings */
  --destructive-foreground: #ffffff;

  /* Game-themed accent colors */
  --neon-cyan: #06b6d4;
  --neon-pink: #ec4899;
  --neon-purple: #8b5cf6;
  --electric-blue: #3b82f6;
  --chaos-orange: #f97316;
  --victory-green: #22c55e;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--neon-purple) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary) 0%, var(--chaos-orange) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent) 0%, var(--neon-cyan) 100%);
  --gradient-chaos: linear-gradient(135deg, var(--neon-pink) 0%, var(--chaos-orange) 50%, var(--electric-blue) 100%);
}

@theme inline {
  /* Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  /* Game-themed colors */
  --color-neon-cyan: var(--neon-cyan);
  --color-neon-pink: var(--neon-pink);
  --color-neon-purple: var(--neon-purple);
  --color-electric-blue: var(--electric-blue);
  --color-chaos-orange: var(--chaos-orange);
  --color-victory-green: var(--victory-green);

  /* Typography */
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  --font-display: 'Orbitron', 'Inter', system-ui, sans-serif;

  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  --font-size-7xl: 4.5rem;
  --font-size-8xl: 6rem;
  --font-size-9xl: 8rem;

  /* Spacing */
  --spacing-px: 1px;
  --spacing-0: 0;
  --spacing-0-5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1-5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2-5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3-5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-11: 2.75rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-28: 7rem;
  --spacing-32: 8rem;
  --spacing-36: 9rem;
  --spacing-40: 10rem;
  --spacing-44: 11rem;
  --spacing-48: 12rem;
  --spacing-52: 13rem;
  --spacing-56: 14rem;
  --spacing-60: 15rem;
  --spacing-64: 16rem;
  --spacing-72: 18rem;
  --spacing-80: 20rem;
  --spacing-96: 24rem;

  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --shadow-none: 0 0 #0000;

  /* Game-themed glows */
  --glow-primary: 0 0 20px var(--primary);
  --glow-secondary: 0 0 20px var(--secondary);
  --glow-accent: 0 0 20px var(--accent);
  --glow-neon-cyan: 0 0 20px var(--neon-cyan);
  --glow-neon-pink: 0 0 20px var(--neon-pink);
  --glow-neon-purple: 0 0 20px var(--neon-purple);
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #ffffff;
    --foreground: #0f172a;
    --muted: #f1f5f9;
    --muted-foreground: #64748b;
    --border: #e2e8f0;
    --input: #f8fafc;
    --ring: #3b82f6;
  }
}

/* Base Styles */
* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-feature-settings: "rlig" 1, "calt" 1;
  line-height: 1.6;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Selection */
::selection {
  background: var(--primary);
  color: var(--primary-foreground);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}
