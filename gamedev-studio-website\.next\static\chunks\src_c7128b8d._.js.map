{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs)\n}\n\n// Button variants for consistent styling across the app\nexport const buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        neon: \"bg-gradient-to-r from-neon-cyan to-neon-purple text-white hover:shadow-[0_0_20px_rgba(6,182,212,0.5)] transition-all duration-300\",\n        chaos: \"bg-gradient-to-r from-neon-pink via-chaos-orange to-electric-blue text-white hover:shadow-[0_0_30px_rgba(236,72,153,0.6)] transition-all duration-300 animate-pulse\",\n        game: \"bg-gradient-to-r from-primary to-accent text-white hover:shadow-[0_0_25px_rgba(99,102,241,0.5)] transition-all duration-300\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\n// Card variants for different content types\nexport const cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        elevated: \"shadow-lg hover:shadow-xl\",\n        neon: \"border-neon-cyan/30 bg-gradient-to-br from-background to-muted hover:border-neon-cyan/50 hover:shadow-[0_0_20px_rgba(6,182,212,0.2)]\",\n        game: \"border-primary/30 bg-gradient-to-br from-background to-primary/5 hover:border-primary/50 hover:shadow-[0_0_20px_rgba(99,102,241,0.2)]\",\n        project: \"border-accent/30 bg-gradient-to-br from-background to-accent/5 hover:border-accent/50 hover:shadow-[0_0_20px_rgba(16,185,129,0.2)] hover:scale-[1.02]\",\n      },\n      padding: {\n        none: \"p-0\",\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\n// Text variants for consistent typography\nexport const textVariants = cva(\"\", {\n  variants: {\n    variant: {\n      default: \"text-foreground\",\n      muted: \"text-muted-foreground\",\n      accent: \"text-accent\",\n      primary: \"text-primary\",\n      secondary: \"text-secondary\",\n      destructive: \"text-destructive\",\n      neon: \"text-transparent bg-clip-text bg-gradient-to-r from-neon-cyan to-neon-purple\",\n      chaos: \"text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-chaos-orange to-electric-blue\",\n      game: \"text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent\",\n    },\n    size: {\n      xs: \"text-xs\",\n      sm: \"text-sm\",\n      base: \"text-base\",\n      lg: \"text-lg\",\n      xl: \"text-xl\",\n      \"2xl\": \"text-2xl\",\n      \"3xl\": \"text-3xl\",\n      \"4xl\": \"text-4xl\",\n      \"5xl\": \"text-5xl\",\n      \"6xl\": \"text-6xl\",\n    },\n    weight: {\n      light: \"font-light\",\n      normal: \"font-normal\",\n      medium: \"font-medium\",\n      semibold: \"font-semibold\",\n      bold: \"font-bold\",\n      extrabold: \"font-extrabold\",\n    },\n    family: {\n      sans: \"font-sans\",\n      mono: \"font-mono\",\n      display: \"font-display\",\n    },\n  },\n  defaultVariants: {\n    variant: \"default\",\n    size: \"base\",\n    weight: \"normal\",\n    family: \"sans\",\n  },\n})\n\n// Animation utilities\nexport const animations = {\n  fadeIn: \"animate-in fade-in duration-500\",\n  slideUp: \"animate-in slide-in-from-bottom-4 duration-500\",\n  slideDown: \"animate-in slide-in-from-top-4 duration-500\",\n  slideLeft: \"animate-in slide-in-from-right-4 duration-500\",\n  slideRight: \"animate-in slide-in-from-left-4 duration-500\",\n  scaleIn: \"animate-in zoom-in-95 duration-300\",\n  bounce: \"animate-bounce\",\n  pulse: \"animate-pulse\",\n  spin: \"animate-spin\",\n  ping: \"animate-ping\",\n}\n\n// Utility functions\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date)\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat(\"en-US\", {\n    style: \"currency\",\n    currency: \"USD\",\n  }).format(amount)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, \"\")\n    .replace(/[\\s_-]+/g, \"-\")\n    .replace(/^-+|-+$/g, \"\")\n}\n\nexport function truncate(text: string, length: number): string {\n  if (text.length <= length) return text\n  return text.slice(0, length) + \"...\"\n}\n\n// Type exports for component props\nexport type ButtonVariants = VariantProps<typeof buttonVariants>\nexport type CardVariants = VariantProps<typeof cardVariants>\nexport type TextVariants = VariantProps<typeof textVariants>\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAGO,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC9B,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAIK,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAC5B,wFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,MAAM;YACN,MAAM;YACN,SAAS;QACX;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AAIK,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAAE,IAAI;IAClC,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;YACT,WAAW;YACX,aAAa;YACb,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA,QAAQ;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,MAAM;YACN,WAAW;QACb;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;AACR;AAGO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn, buttonVariants, type ButtonVariants } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    ButtonVariants {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { Menu, X, Gamepad2, Zap } from \"lucide-react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { cn } from \"@/lib/utils\"\n\nconst navigation = [\n  { name: \"Home\", href: \"/\" },\n  { name: \"About\", href: \"/about\" },\n  { name: \"Projects\", href: \"/projects\" },\n  { name: \"Blog\", href: \"/blog\" },\n  { name: \"Contact\", href: \"/contact\" },\n]\n\nexport function Header() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"relative\">\n              <Gamepad2 className=\"h-8 w-8 text-primary transition-colors group-hover:text-neon-cyan\" />\n              <Zap className=\"absolute -top-1 -right-1 h-4 w-4 text-secondary animate-pulse\" />\n            </div>\n            <span className=\"text-xl font-display font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n              Chaos Studios\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  \"text-sm font-medium transition-colors hover:text-primary relative group\",\n                  pathname === item.href\n                    ? \"text-primary\"\n                    : \"text-muted-foreground\"\n                )}\n              >\n                {item.name}\n                {pathname === item.href && (\n                  <motion.div\n                    layoutId=\"activeTab\"\n                    className=\"absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent\"\n                    initial={false}\n                    transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                  />\n                )}\n                <div className=\"absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent scale-x-0 group-hover:scale-x-100 transition-transform duration-300\" />\n              </Link>\n            ))}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button asChild variant=\"game\" size=\"sm\">\n              <Link href=\"/request-quote\">\n                Request Quote\n              </Link>\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden\"\n            onClick={() => setIsOpen(!isOpen)}\n            aria-label=\"Toggle menu\"\n          >\n            {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n          </Button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden border-t border-border/40 bg-background/95 backdrop-blur\"\n          >\n            <div className=\"container mx-auto px-4 py-4\">\n              <nav className=\"flex flex-col space-y-4\">\n                {navigation.map((item, index) => (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                  >\n                    <Link\n                      href={item.href}\n                      className={cn(\n                        \"block text-sm font-medium transition-colors hover:text-primary\",\n                        pathname === item.href\n                          ? \"text-primary\"\n                          : \"text-muted-foreground\"\n                      )}\n                      onClick={() => setIsOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  </motion.div>\n                ))}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: navigation.length * 0.1 }}\n                  className=\"pt-4 border-t border-border/40\"\n                >\n                  <Button asChild variant=\"game\" size=\"sm\" className=\"w-full\">\n                    <Link href=\"/request-quote\" onClick={() => setIsOpen(false)}>\n                      Request Quote\n                    </Link>\n                  </Button>\n                </motion.div>\n              </nav>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AARA;;;;;;;;AAUA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAuG;;;;;;;;;;;;sCAMzH,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2EACA,aAAa,KAAK,IAAI,GAClB,iBACA;;wCAGL,KAAK,IAAI;wCACT,aAAa,KAAK,IAAI,kBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,UAAS;4CACT,WAAU;4CACV,SAAS;4CACT,YAAY;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;;;;;;sDAG9D,6LAAC;4CAAI,WAAU;;;;;;;mCAlBV,KAAK,IAAI;;;;;;;;;;sCAwBpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qJAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,SAAQ;gCAAO,MAAK;0CAClC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAiB;;;;;;;;;;;;;;;;sCAOhC,6LAAC,qJAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,UAAU,CAAC;4BAC1B,cAAW;sCAEV,uBAAS,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5D,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;kDAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,aAAa,KAAK,IAAI,GAClB,iBACA;4CAEN,SAAS,IAAM,UAAU;sDAExB,KAAK,IAAI;;;;;;uCAfP,KAAK,IAAI;;;;;8CAmBlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,WAAW,MAAM,GAAG;oCAAI;oCAC7C,WAAU;8CAEV,cAAA,6LAAC,qJAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAO,MAAK;wCAAK,WAAU;kDACjD,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAiB,SAAS,IAAM,UAAU;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjF;GAxHgB;;QAEG,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Gamepad2, Zap, Github, Twitter, Linkedin, Mail, MapPin, Phone } from \"lucide-react\"\n\nconst footerLinks = {\n  company: [\n    { name: \"About Us\", href: \"/about\" },\n    { name: \"Our Team\", href: \"/about#team\" },\n    { name: \"Careers\", href: \"/careers\" },\n    { name: \"Press Kit\", href: \"/press\" },\n  ],\n  services: [\n    { name: \"Game Development\", href: \"/services/game-development\" },\n    { name: \"Asset Creation\", href: \"/services/asset-creation\" },\n    { name: \"Consulting\", href: \"/services/consulting\" },\n    { name: \"Porting\", href: \"/services/porting\" },\n  ],\n  resources: [\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Case Studies\", href: \"/case-studies\" },\n    { name: \"Documentation\", href: \"/docs\" },\n    { name: \"Support\", href: \"/support\" },\n  ],\n  legal: [\n    { name: \"Privacy Policy\", href: \"/privacy\" },\n    { name: \"Terms of Service\", href: \"/terms\" },\n    { name: \"Cookie Policy\", href: \"/cookies\" },\n    { name: \"GDPR\", href: \"/gdpr\" },\n  ],\n}\n\nconst socialLinks = [\n  { name: \"GitHub\", href: \"https://github.com/chaosstudios\", icon: Github },\n  { name: \"Twitter\", href: \"https://twitter.com/chaosstudios\", icon: Twitter },\n  { name: \"LinkedIn\", href: \"https://linkedin.com/company/chaosstudios\", icon: Linkedin },\n  { name: \"Email\", href: \"mailto:<EMAIL>\", icon: Mail },\n]\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-muted/30 border-t border-border/40\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-12 lg:py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-2 group mb-4\">\n                <div className=\"relative\">\n                  <Gamepad2 className=\"h-8 w-8 text-primary transition-colors group-hover:text-neon-cyan\" />\n                  <Zap className=\"absolute -top-1 -right-1 h-4 w-4 text-secondary animate-pulse\" />\n                </div>\n                <span className=\"text-xl font-display font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n                  Chaos Studios\n                </span>\n              </Link>\n              <p className=\"text-muted-foreground mb-6 max-w-sm\">\n                Crafting chaotic-strategic games and interactive experiences that push the boundaries of digital entertainment.\n              </p>\n              \n              {/* Contact Info */}\n              <div className=\"space-y-2 text-sm text-muted-foreground\">\n                <div className=\"flex items-center space-x-2\">\n                  <MapPin className=\"h-4 w-4\" />\n                  <span>San Francisco, CA</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Phone className=\"h-4 w-4\" />\n                  <span>+****************</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <Mail className=\"h-4 w-4\" />\n                  <span><EMAIL></span>\n                </div>\n              </div>\n            </div>\n\n            {/* Links Sections */}\n            <div>\n              <h3 className=\"font-semibold text-foreground mb-4\">Company</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-foreground mb-4\">Services</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-foreground mb-4\">Resources</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold text-foreground mb-4\">Legal</h3>\n              <ul className=\"space-y-2\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"py-6 border-t border-border/40\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-sm text-muted-foreground\">\n              © {new Date().getFullYear()} Chaos Studios. All rights reserved.\n            </div>\n            \n            {/* Social Links */}\n            <div className=\"flex items-center space-x-4\">\n              {socialLinks.map((social) => {\n                const Icon = social.icon\n                return (\n                  <Link\n                    key={social.name}\n                    href={social.href}\n                    className=\"text-muted-foreground hover:text-primary transition-colors\"\n                    aria-label={social.name}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAS;QACnC;YAAE,MAAM;YAAY,MAAM;QAAc;QACxC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAa,MAAM;QAAS;KACrC;IACD,UAAU;QACR;YAAE,MAAM;YAAoB,MAAM;QAA6B;QAC/D;YAAE,MAAM;YAAkB,MAAM;QAA2B;QAC3D;YAAE,MAAM;YAAc,MAAM;QAAuB;QACnD;YAAE,MAAM;YAAW,MAAM;QAAoB;KAC9C;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAgB,MAAM;QAAgB;QAC9C;YAAE,MAAM;YAAiB,MAAM;QAAQ;QACvC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,OAAO;QACL;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAoB,MAAM;QAAS;QAC3C;YAAE,MAAM;YAAiB,MAAM;QAAW;QAC1C;YAAE,MAAM;YAAQ,MAAM;QAAQ;KAC/B;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAU,MAAM;QAAmC,MAAM,yMAAA,CAAA,SAAM;IAAC;IACxE;QAAE,MAAM;QAAW,MAAM;QAAoC,MAAM,2MAAA,CAAA,UAAO;IAAC;IAC3E;QAAE,MAAM;QAAY,MAAM;QAA6C,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACtF;QAAE,MAAM;QAAS,MAAM;QAAiC,MAAM,qMAAA,CAAA,OAAI;IAAC;CACpE;AAEM,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;0DAEjB,6LAAC;gDAAK,WAAU;0DAAuG;;;;;;;;;;;;kDAIzH,6LAAC;wCAAE,WAAU;kDAAsC;;;;;;kDAKnD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAgC;oCAC1C,IAAI,OAAO,WAAW;oCAAG;;;;;;;0CAI9B,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC;oCAChB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,OAAO,IAAI;wCACjB,WAAU;wCACV,cAAY,OAAO,IAAI;kDAEvB,cAAA,6LAAC;4CAAK,WAAU;;;;;;uCALX,OAAO,IAAI;;;;;gCAQtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;KAtIgB", "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/layout/main-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { <PERSON><PERSON> } from \"./header\"\nimport { Footer } from \"./footer\"\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nconst pageVariants = {\n  initial: {\n    opacity: 0,\n    y: 20,\n  },\n  in: {\n    opacity: 1,\n    y: 0,\n  },\n  out: {\n    opacity: 0,\n    y: -20,\n  },\n}\n\nconst pageTransition = {\n  type: \"tween\",\n  ease: \"anticipate\",\n  duration: 0.5,\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header />\n      <motion.main\n        className=\"flex-1\"\n        initial=\"initial\"\n        animate=\"in\"\n        exit=\"out\"\n        variants={pageVariants}\n        transition={pageTransition}\n      >\n        {children}\n      </motion.main>\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUA,MAAM,eAAe;IACnB,SAAS;QACP,SAAS;QACT,GAAG;IACL;IACA,IAAI;QACF,SAAS;QACT,GAAG;IACL;IACA,KAAK;QACH,SAAS;QACT,GAAG,CAAC;IACN;AACF;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAEO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,SAAM;;;;;0BACP,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,WAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,MAAK;gBACL,UAAU;gBACV,YAAY;0BAEX;;;;;;0BAEH,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;AAGb;KAjBgB", "debugId": null}}]}