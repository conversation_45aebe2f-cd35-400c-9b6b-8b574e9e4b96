{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs)\n}\n\n// Button variants for consistent styling across the app\nexport const buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        neon: \"bg-gradient-to-r from-neon-cyan to-neon-purple text-white hover:shadow-[0_0_20px_rgba(6,182,212,0.5)] transition-all duration-300\",\n        chaos: \"bg-gradient-to-r from-neon-pink via-chaos-orange to-electric-blue text-white hover:shadow-[0_0_30px_rgba(236,72,153,0.6)] transition-all duration-300 animate-pulse\",\n        game: \"bg-gradient-to-r from-primary to-accent text-white hover:shadow-[0_0_25px_rgba(99,102,241,0.5)] transition-all duration-300\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\n// Card variants for different content types\nexport const cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        elevated: \"shadow-lg hover:shadow-xl\",\n        neon: \"border-neon-cyan/30 bg-gradient-to-br from-background to-muted hover:border-neon-cyan/50 hover:shadow-[0_0_20px_rgba(6,182,212,0.2)]\",\n        game: \"border-primary/30 bg-gradient-to-br from-background to-primary/5 hover:border-primary/50 hover:shadow-[0_0_20px_rgba(99,102,241,0.2)]\",\n        project: \"border-accent/30 bg-gradient-to-br from-background to-accent/5 hover:border-accent/50 hover:shadow-[0_0_20px_rgba(16,185,129,0.2)] hover:scale-[1.02]\",\n      },\n      padding: {\n        none: \"p-0\",\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\n// Text variants for consistent typography\nexport const textVariants = cva(\"\", {\n  variants: {\n    variant: {\n      default: \"text-foreground\",\n      muted: \"text-muted-foreground\",\n      accent: \"text-accent\",\n      primary: \"text-primary\",\n      secondary: \"text-secondary\",\n      destructive: \"text-destructive\",\n      neon: \"text-transparent bg-clip-text bg-gradient-to-r from-neon-cyan to-neon-purple\",\n      chaos: \"text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-chaos-orange to-electric-blue\",\n      game: \"text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent\",\n    },\n    size: {\n      xs: \"text-xs\",\n      sm: \"text-sm\",\n      base: \"text-base\",\n      lg: \"text-lg\",\n      xl: \"text-xl\",\n      \"2xl\": \"text-2xl\",\n      \"3xl\": \"text-3xl\",\n      \"4xl\": \"text-4xl\",\n      \"5xl\": \"text-5xl\",\n      \"6xl\": \"text-6xl\",\n    },\n    weight: {\n      light: \"font-light\",\n      normal: \"font-normal\",\n      medium: \"font-medium\",\n      semibold: \"font-semibold\",\n      bold: \"font-bold\",\n      extrabold: \"font-extrabold\",\n    },\n    family: {\n      sans: \"font-sans\",\n      mono: \"font-mono\",\n      display: \"font-display\",\n    },\n  },\n  defaultVariants: {\n    variant: \"default\",\n    size: \"base\",\n    weight: \"normal\",\n    family: \"sans\",\n  },\n})\n\n// Animation utilities\nexport const animations = {\n  fadeIn: \"animate-in fade-in duration-500\",\n  slideUp: \"animate-in slide-in-from-bottom-4 duration-500\",\n  slideDown: \"animate-in slide-in-from-top-4 duration-500\",\n  slideLeft: \"animate-in slide-in-from-right-4 duration-500\",\n  slideRight: \"animate-in slide-in-from-left-4 duration-500\",\n  scaleIn: \"animate-in zoom-in-95 duration-300\",\n  bounce: \"animate-bounce\",\n  pulse: \"animate-pulse\",\n  spin: \"animate-spin\",\n  ping: \"animate-ping\",\n}\n\n// Utility functions\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(date)\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat(\"en-US\", {\n    style: \"currency\",\n    currency: \"USD\",\n  }).format(amount)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, \"\")\n    .replace(/[\\s_-]+/g, \"-\")\n    .replace(/^-+|-+$/g, \"\")\n}\n\nexport function truncate(text: string, length: number): string {\n  if (text.length <= length) return text\n  return text.slice(0, length) + \"...\"\n}\n\n// Type exports for component props\nexport type ButtonVariants = VariantProps<typeof buttonVariants>\nexport type CardVariants = VariantProps<typeof cardVariants>\nexport type TextVariants = VariantProps<typeof textVariants>\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAGO,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAC9B,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAIK,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAC5B,wFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,MAAM;YACN,MAAM;YACN,SAAS;QACX;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AAIK,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAAE,IAAI;IAClC,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;YACT,WAAW;YACX,aAAa;YACb,MAAM;YACN,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA,QAAQ;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,MAAM;YACN,WAAW;QACb;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;AACR;AAGO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn, buttonVariants, type ButtonVariants } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    ButtonVariants {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn, cardVariants, type CardVariants } from \"@/lib/utils\"\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    CardVariants {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, padding, className }))}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE;YAAE;YAAS;YAAS;QAAU;QACxD,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx <module evaluation>\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mDACA", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/sections/featured-projects.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FeaturedProjects = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeaturedProjects() from the server but FeaturedProjects is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/featured-projects.tsx <module evaluation>\",\n    \"FeaturedProjects\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+EACA", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/components/sections/featured-projects.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FeaturedProjects = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeaturedProjects() from the server but FeaturedProjects is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/featured-projects.tsx\",\n    \"FeaturedProjects\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2DACA", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/GameDev%20Studio%20Website/gamedev-studio-website/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { MainLayout } from \"@/components/layout/main-layout\"\nimport { FeaturedProjects } from \"@/components/sections/featured-projects\"\nimport { Gamepad2, Z<PERSON>, <PERSON>, Users, Trophy, ArrowRight } from \"lucide-react\"\n\n\"use client\"\n\nimport { motion } from \"framer-motion\"\n\nexport default function Home() {\n  return (\n    <MainLayout>\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden bg-gradient-to-br from-background via-background to-primary/5\">\n        <div className=\"absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]\" />\n        <div className=\"relative container mx-auto px-4 py-24 lg:py-32\">\n          <div className=\"text-center max-w-4xl mx-auto\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.5 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5 }}\n              className=\"flex justify-center mb-8\"\n            >\n              <div className=\"relative\">\n                <Gamepad2 className=\"h-16 w-16 text-primary animate-pulse\" />\n                <Zap className=\"absolute -top-2 -right-2 h-8 w-8 text-secondary animate-bounce\" />\n              </div>\n            </motion.div>\n\n            <motion.h1\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"text-4xl md:text-6xl lg:text-7xl font-display font-bold mb-6\"\n            >\n              <span className=\"bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent\">\n                Chaos Studios\n              </span>\n            </motion.h1>\n\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              className=\"text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto\"\n            >\n              Crafting <span className=\"text-primary font-semibold\">chaotic-strategic games</span> and\n              <span className=\"text-accent font-semibold\"> interactive experiences</span> that push the boundaries of digital entertainment.\n            </motion.p>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.6 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            >\n              <Button asChild variant=\"game\" size=\"xl\">\n                <Link href=\"/request-quote\">\n                  Start Your Project\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Link>\n              </Button>\n              <Button asChild variant=\"outline\" size=\"xl\">\n                <Link href=\"/projects\">\n                  View Our Work\n                </Link>\n              </Button>\n            </motion.div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-24 bg-muted/30\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-display font-bold mb-4\">\n              Why Choose <span className=\"text-primary\">Chaos Studios</span>?\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              We combine technical expertise with creative vision to deliver exceptional gaming experiences.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <Card variant=\"game\" className=\"text-center\">\n              <CardHeader>\n                <div className=\"mx-auto mb-4 p-3 bg-primary/10 rounded-full w-fit\">\n                  <Rocket className=\"h-8 w-8 text-primary\" />\n                </div>\n                <CardTitle>Innovation First</CardTitle>\n                <CardDescription>\n                  Cutting-edge technology meets creative storytelling in every project we undertake.\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card variant=\"game\" className=\"text-center\">\n              <CardHeader>\n                <div className=\"mx-auto mb-4 p-3 bg-accent/10 rounded-full w-fit\">\n                  <Users className=\"h-8 w-8 text-accent\" />\n                </div>\n                <CardTitle>Expert Team</CardTitle>\n                <CardDescription>\n                  Seasoned developers, artists, and designers with years of industry experience.\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card variant=\"game\" className=\"text-center\">\n              <CardHeader>\n                <div className=\"mx-auto mb-4 p-3 bg-secondary/10 rounded-full w-fit\">\n                  <Trophy className=\"h-8 w-8 text-secondary\" />\n                </div>\n                <CardTitle>Proven Results</CardTitle>\n                <CardDescription>\n                  Award-winning games and satisfied clients across multiple platforms and genres.\n                </CardDescription>\n              </CardHeader>\n            </Card>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Projects Section */}\n      <FeaturedProjects />\n\n      {/* CTA Section */}\n      <section className=\"py-24 bg-gradient-to-r from-primary/10 via-accent/10 to-secondary/10\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-display font-bold mb-6\">\n            Ready to Create Something <span className=\"text-neon-cyan\">Epic</span>?\n          </h2>\n          <p className=\"text-lg text-muted-foreground mb-8 max-w-2xl mx-auto\">\n            Let's discuss your vision and turn it into an unforgettable gaming experience.\n          </p>\n          <Button asChild variant=\"chaos\" size=\"xl\">\n            <Link href=\"/request-quote\">\n              Get Started Today\n              <Zap className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </Button>\n        </div>\n      </section>\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;;;;;;;;AAFA;;AAIe,SAAS;IACtB,qBACE,8OAAC,8IAAA,CAAA,aAAU;;0BAET,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wJAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAInB,8OAAC,wJAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAEV,cAAA,8OAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;8CAKxG,8OAAC,wJAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;wCACX;sDACU,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;wCAA8B;sDACpF,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;wCAA+B;;;;;;;8CAG7E,8OAAC,wJAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC,kJAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,SAAQ;4CAAO,MAAK;sDAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;;oDAAiB;kEAE1B,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG1B,8OAAC,kJAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,SAAQ;4CAAU,MAAK;sDACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAmD;sDACpD,8OAAC;4CAAK,WAAU;sDAAe;;;;;;wCAAoB;;;;;;;8CAEhE,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAKjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;oCAAC,SAAQ;oCAAO,WAAU;8CAC7B,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,SAAQ;oCAAO,WAAU;8CAC7B,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,SAAQ;oCAAO,WAAU;8CAC7B,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3B,8OAAC,sJAAA,CAAA,mBAAgB;;;;;0BAGjB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAmD;8CACrC,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;gCAAW;;;;;;;sCAExE,8OAAC;4BAAE,WAAU;sCAAuD;;;;;;sCAGpE,8OAAC,kJAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,SAAQ;4BAAQ,MAAK;sCACnC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;;oCAAiB;kDAE1B,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}]}