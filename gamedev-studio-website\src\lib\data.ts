// Sample data for the website

export interface Project {
  id: string
  title: string
  description: string
  image: string
  category: string
  status: "completed" | "in-progress" | "upcoming"
  technologies: string[]
  featured: boolean
  link?: string
}

export interface TeamMember {
  id: string
  name: string
  role: string
  bio: string
  image: string
  social: {
    twitter?: string
    linkedin?: string
    github?: string
  }
}

export interface BlogPost {
  id: string
  title: string
  excerpt: string
  content: string
  author: string
  publishedAt: string
  category: string
  tags: string[]
  featured: boolean
  image: string
}

// Sample Projects Data
export const projects: Project[] = [
  {
    id: "pigeon-turf-wars",
    title: "Pigeon Turf Wars",
    description: "A chaotic-strategic multiplayer game where players control flocks of pigeons competing for urban territory. Features real-time strategy elements with unpredictable AI behaviors.",
    image: "/projects/pigeon-turf-wars.jpg",
    category: "Strategy",
    status: "completed",
    technologies: ["Unity", "C#", "Photon", "Steam API"],
    featured: true,
    link: "https://store.steampowered.com/app/pigeon-turf-wars"
  },
  {
    id: "neon-runner",
    title: "Neon Runner",
    description: "A fast-paced cyberpunk endless runner with dynamic music synchronization and procedurally generated obstacles. Built for mobile and PC platforms.",
    image: "/projects/neon-runner.jpg",
    category: "Action",
    status: "completed",
    technologies: ["Unreal Engine", "Blueprint", "Mobile SDK"],
    featured: true
  },
  {
    id: "quantum-chess",
    title: "Quantum Chess",
    description: "Traditional chess meets quantum mechanics. Pieces exist in superposition states until observed, creating mind-bending strategic possibilities.",
    image: "/projects/quantum-chess.jpg",
    category: "Puzzle",
    status: "in-progress",
    technologies: ["React", "Three.js", "WebGL", "Node.js"],
    featured: true
  },
  {
    id: "mystic-forge",
    title: "Mystic Forge",
    description: "A magical crafting simulation where players discover recipes through experimentation and intuition rather than following guides.",
    image: "/projects/mystic-forge.jpg",
    category: "Simulation",
    status: "upcoming",
    technologies: ["Godot", "GDScript", "SQLite"],
    featured: false
  }
]

// Sample Team Members Data
export const teamMembers: TeamMember[] = [
  {
    id: "alex-chen",
    name: "Alex Chen",
    role: "Creative Director & Founder",
    bio: "Former AAA game designer with 10+ years experience. Led development teams at major studios before founding Chaos Studios to create innovative indie experiences.",
    image: "/team/alex-chen.jpg",
    social: {
      twitter: "@alexchen_dev",
      linkedin: "alexchen-gamedev",
      github: "alexchen"
    }
  },
  {
    id: "maya-rodriguez",
    name: "Maya Rodriguez",
    role: "Lead Developer",
    bio: "Full-stack developer specializing in game engines and real-time systems. Expert in Unity, Unreal Engine, and custom engine development.",
    image: "/team/maya-rodriguez.jpg",
    social: {
      twitter: "@maya_codes",
      linkedin: "maya-rodriguez-dev",
      github: "mayarod"
    }
  },
  {
    id: "jordan-kim",
    name: "Jordan Kim",
    role: "Art Director",
    bio: "Visual artist with a passion for creating immersive game worlds. Specializes in concept art, 3D modeling, and procedural generation techniques.",
    image: "/team/jordan-kim.jpg",
    social: {
      linkedin: "jordan-kim-art",
      twitter: "@jordankim_art"
    }
  },
  {
    id: "sam-okafor",
    name: "Sam Okafor",
    role: "Audio Designer",
    bio: "Sound engineer and composer creating dynamic audio experiences. Expert in adaptive music systems and spatial audio for immersive gameplay.",
    image: "/team/sam-okafor.jpg",
    social: {
      twitter: "@sam_audio",
      linkedin: "sam-okafor-audio"
    }
  }
]

// Sample Blog Posts Data
export const blogPosts: BlogPost[] = [
  {
    id: "chaos-in-game-design",
    title: "Embracing Chaos in Game Design",
    excerpt: "How unpredictability and emergent gameplay can create more engaging player experiences.",
    content: "Full blog post content here...",
    author: "Alex Chen",
    publishedAt: "2024-01-15",
    category: "Design",
    tags: ["game design", "chaos theory", "emergent gameplay"],
    featured: true,
    image: "/blog/chaos-in-game-design.jpg"
  },
  {
    id: "unity-optimization-tips",
    title: "Unity Performance Optimization: 10 Essential Tips",
    excerpt: "Practical techniques to improve your Unity game's performance across all platforms.",
    content: "Full blog post content here...",
    author: "Maya Rodriguez",
    publishedAt: "2024-01-10",
    category: "Development",
    tags: ["unity", "optimization", "performance"],
    featured: true,
    image: "/blog/unity-optimization.jpg"
  },
  {
    id: "procedural-art-generation",
    title: "The Art of Procedural Generation",
    excerpt: "Exploring how algorithms can create beautiful and diverse game art assets.",
    content: "Full blog post content here...",
    author: "Jordan Kim",
    publishedAt: "2024-01-05",
    category: "Art",
    tags: ["procedural generation", "art", "algorithms"],
    featured: false,
    image: "/blog/procedural-art.jpg"
  }
]

// Helper functions
export function getFeaturedProjects(): Project[] {
  return projects.filter(project => project.featured)
}

export function getFeaturedBlogPosts(): BlogPost[] {
  return blogPosts.filter(post => post.featured)
}

export function getProjectsByCategory(category: string): Project[] {
  return projects.filter(project => project.category === category)
}

export function getProjectsByStatus(status: Project["status"]): Project[] {
  return projects.filter(project => project.status === status)
}
