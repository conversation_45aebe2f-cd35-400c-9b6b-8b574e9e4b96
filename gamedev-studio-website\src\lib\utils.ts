import { type ClassValue, clsx } from "clsx"
import { cva, type VariantProps } from "class-variance-authority"

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs)
}

// Button variants for consistent styling across the app
export const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        neon: "bg-gradient-to-r from-neon-cyan to-neon-purple text-white hover:shadow-[0_0_20px_rgba(6,182,212,0.5)] transition-all duration-300",
        chaos: "bg-gradient-to-r from-neon-pink via-chaos-orange to-electric-blue text-white hover:shadow-[0_0_30px_rgba(236,72,153,0.6)] transition-all duration-300 animate-pulse",
        game: "bg-gradient-to-r from-primary to-accent text-white hover:shadow-[0_0_25px_rgba(99,102,241,0.5)] transition-all duration-300",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        xl: "h-12 rounded-lg px-10 text-base",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

// Card variants for different content types
export const cardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300",
  {
    variants: {
      variant: {
        default: "border-border",
        elevated: "shadow-lg hover:shadow-xl",
        neon: "border-neon-cyan/30 bg-gradient-to-br from-background to-muted hover:border-neon-cyan/50 hover:shadow-[0_0_20px_rgba(6,182,212,0.2)]",
        game: "border-primary/30 bg-gradient-to-br from-background to-primary/5 hover:border-primary/50 hover:shadow-[0_0_20px_rgba(99,102,241,0.2)]",
        project: "border-accent/30 bg-gradient-to-br from-background to-accent/5 hover:border-accent/50 hover:shadow-[0_0_20px_rgba(16,185,129,0.2)] hover:scale-[1.02]",
      },
      padding: {
        none: "p-0",
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
      },
    },
    defaultVariants: {
      variant: "default",
      padding: "default",
    },
  }
)

// Text variants for consistent typography
export const textVariants = cva("", {
  variants: {
    variant: {
      default: "text-foreground",
      muted: "text-muted-foreground",
      accent: "text-accent",
      primary: "text-primary",
      secondary: "text-secondary",
      destructive: "text-destructive",
      neon: "text-transparent bg-clip-text bg-gradient-to-r from-neon-cyan to-neon-purple",
      chaos: "text-transparent bg-clip-text bg-gradient-to-r from-neon-pink via-chaos-orange to-electric-blue",
      game: "text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent",
    },
    size: {
      xs: "text-xs",
      sm: "text-sm",
      base: "text-base",
      lg: "text-lg",
      xl: "text-xl",
      "2xl": "text-2xl",
      "3xl": "text-3xl",
      "4xl": "text-4xl",
      "5xl": "text-5xl",
      "6xl": "text-6xl",
    },
    weight: {
      light: "font-light",
      normal: "font-normal",
      medium: "font-medium",
      semibold: "font-semibold",
      bold: "font-bold",
      extrabold: "font-extrabold",
    },
    family: {
      sans: "font-sans",
      mono: "font-mono",
      display: "font-display",
    },
  },
  defaultVariants: {
    variant: "default",
    size: "base",
    weight: "normal",
    family: "sans",
  },
})

// Animation utilities
export const animations = {
  fadeIn: "animate-in fade-in duration-500",
  slideUp: "animate-in slide-in-from-bottom-4 duration-500",
  slideDown: "animate-in slide-in-from-top-4 duration-500",
  slideLeft: "animate-in slide-in-from-right-4 duration-500",
  slideRight: "animate-in slide-in-from-left-4 duration-500",
  scaleIn: "animate-in zoom-in-95 duration-300",
  bounce: "animate-bounce",
  pulse: "animate-pulse",
  spin: "animate-spin",
  ping: "animate-ping",
}

// Utility functions
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(date)
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount)
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, "")
    .replace(/[\s_-]+/g, "-")
    .replace(/^-+|-+$/g, "")
}

export function truncate(text: string, length: number): string {
  if (text.length <= length) return text
  return text.slice(0, length) + "..."
}

// Type exports for component props
export type ButtonVariants = VariantProps<typeof buttonVariants>
export type CardVariants = VariantProps<typeof cardVariants>
export type TextVariants = VariantProps<typeof textVariants>
